{"GlobalPropertiesHash": "CfyydZsxqnstpa5/KDOwXnfO5UFDXq2b6uQ1zvDV/2M=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2fXJhy1TpFde0mPyAhr9n/IsE2gyYh7vOszKbTs2G3o=", "TCPtnki7ov6WFMzZzswWE4xoUfb0BkY8mfU9IyQZPz8=", "rK2Z0YIGU3lHQQrxIj6YJZVaFFxKFsA4wklPjSdXytI=", "49GQXRbC1u/3zXttpF0M+EsgSuunWD5HSlM0W9Ll9yE=", "W72E8vEJPAFD1ToDN33+2NBvgQ9zrJu6Ljd3nCfj+ik=", "R7PAKo20yyqxanXCvJrTJqUIMn0sbRR66bRrLjJVRUs=", "jgtGZGofNSMGMa31DbGvsIAzuvtOe8yoqq7wD8PV1UA=", "rFz1CHCHQQtYvUKIPcHI/LOy0+hVWN37TlJ/gW4O3WI=", "luoGfjdRQw5YOWyfK6VX1zsj6AN7WdejwIY6/Wqj0rU=", "yg7pJRuNadYqKM23npWh/CaFBvXT4R1y8obB2snEE18=", "WikkFaObNia+HRrcHc3LjcNtHmkyQZmZFsKsy6NU2Bg=", "16iVAqTtMzq7Gnza59iYZhIrF+7sHQ7lcKCn43b2NFI=", "7HBEjKiOONnOcQ7aVWkkammvNi17HzDjbpnJd/fwSqs=", "da4adgL7OvhhKXuZJRvx2A0FU1YpYYUUBtUhMlqqJPQ=", "yptICzWMSa+07wOxJuywWQ5uxu0yomADuJhCS+k3y7E=", "Ssxy7Ruu5tAPzd6TZu++vYOq078VePGBTWtHmQxtrBE=", "zKEJTKyV8qtAiBlNdC5TLGHIX2cIy7PGeZMGMy2+liA=", "EY3Kza51WBHLYS1Wzunr1AAL5FzhZ+O6cZDahb9Dw6c=", "nLoZNMdYmNby5HhG+8fArHUXlwXrLOnr88vh3Y6gU1U=", "RBgidQmzt42sadbGC0MDOML510wSZ9ua9oTQK5o3xXE=", "f74lm5nqIXq3jIxa/TaVKhVeTmSkKOPaNWjl9nlFhjc=", "hH70RGwPUlePtfGntg3WhAR1crhTAt/gjLjG8Zc6nog=", "sL0Gd/vbq71z2MI56mrL5/4HUv9G/gbhqQA/ndlLqf0=", "QO25HBa2HcjsMISSSB1xw6hcMGGikLsDHY9k1KElLOw=", "ONwztavErtsKeMXDlPWuqfnUSYCZEyq3qIh7ZeKf7J8=", "/P766NzCgNdPgCa4FkQgMJ2scSFFrSwaK+j4nOar7OM=", "KGwsGd+ItqfTYTEE2elExkcrP01/+3wtLKE/yHEyjYU=", "CdwiO55NVzhq5GJDNJgJ/NPS+pWdv7Vn1rtkdNhQcfc=", "t8sutfiFrf5WSr+zMVzyFjGD414jC2+rfhm5DDoAYVQ=", "Cz7sns+uSSfTUPJ9kPyGI/f79niY2UuqmZZ1Bf1uRDs=", "IBMgzlLWsEYqjkvjTZNbHZ7/ma497Yz52Y5EyMwUTpY=", "mJuuGzdkj75g5gvZhNMdmRs0BX9CJde8qw0W0WDQaEw=", "clyo4q+3cxRbVsQIeKKBpK87QOLFv8b5yC9HHwysN20=", "eIA+4gjb//b1VlVu+1KW57BFscWYXY/ruRithSZGTMk=", "avorWBGlAEnLLV+abN/MaLAiVZnazdwUv9tTb99Hx40=", "1z9XoDhor8kjhqIkdpqh0XwU2BfPiQYWejsf3esroIQ=", "+cFYkXHSyeRCO7ZWpmoGZIu4Cik53Ub2BHodmTxXxBE=", "F5/UtgVLgJoVrNVIjB/QOna7aWB67RKnNFuwZEmfZOs=", "QFHo8IXxl0XIQm72+5EBlvZtk77bX9n12tlfDwVYVhU=", "+fko3tMf13UT7wEQxj1ckU/ANu9empWlY+vNAHXthFQ=", "RjSW/E6dhXB/8iOkg0iAilBUR0LAwIOsr9tj4+mX/BE=", "w0wycFNYSFDSf7XqvDCxtIzhgxI5mK2bHBP8BbKHZhM=", "Mza7gGWJonDiMHgEtwaBwU0scBmPoB0oAFDvvGDrvoQ=", "o3O3Uu+7IKPdSFQa5P2Knly1gFebQwIBKuaCkzDNdeY=", "/5lOtZvartputSkB4WbIGce3GBJCu1lB8lrXF7Rhago=", "fOj5M8a7wTFVjbAQSi8/ZvV3Atqu/xj+IOmTsDedruk=", "IM2F3dZhp96ZsuYBr9gXRwXLArEo9lF6FrgZCz+xAV8=", "ilhgdB4n0rKldDIrmBK4YPiViZ88Pt+WTb5oEeZoGls=", "90cFAjBqc61xfREx2a0TisyzElBdwRRn/8xB2pmFxwc=", "zlufR+YLJjVLanhqdYnQO0NRfNQtFJEOsSR/gWA0TG4=", "Jtk0GlIkg+WX2h8IUU17qC5ThSb+56flKlvRdWcW6H4=", "l8i4ydodlDXb/iAkzzvPzXqXkC/ehZ9kkqDwAIzcRbs=", "mCyPgIKsKS2IMexa2nP6d1v+xSpjD1ahvuItjzmDTOE=", "wqnrBdFQawZyh8dF3gXflYnkZD+8m6bpgjLg2Rc+62w=", "f0XL+0YkFqEk3avYX0Eb6XFmHXDIOoRdVJaZQkOYzEU=", "3ZKxbnVesTlQ0o+3Hzec8xyri18P3wTD11iwj5IQhwc=", "b7REs2Fff3uPO0Hi1pcOkBM6DdDJ0gPkDJcqyuIgGio=", "rxxPSsqF/NDxRAs2mO76LrfxojTEfXZFbB+tnDS0IUk=", "we8G4LVKehAwA1JGrY+KD5IcqefOBVOddrJC+WVBFac=", "f3vrDiKWFzcgwVLhfngB9VRvZHHgcH0b9PGtrUbMwOg=", "i3pSuWFMSVi8ldHOHFnDSdz4TcxOPIgygsCwD1YwTnQ=", "g47QnuRG60+3NmDHluY2DdCy3ZD7+PLvbBm5Q4ff5WY=", "7PbJVjBqBGGW8wPp3ysAx1HA6KWpKlrAipmkK0QglqU=", "0KozhU0S4hHKbI0KCwFMnk65UVysz1tEzwKibjGvDmg=", "DeRVbWJc2/Qxwt+pvkIyMgaV1/L9jw4jxcOBE5CAw7c=", "oAGUggOYCcYsZsLbDQRrER/MXI1X/g8Pusn6l9nvgPw=", "YOd+NRJBQrjv1uMWQ0rJdVs2+Lu186Z8gDHbVchTWyE=", "8XvQ1UZZ9OHB2aJvL3HHjlWazLnH0eEUIdiy2sa8JdE=", "5JfX/4BQC0AK1NiylYECmWxZJoRpYsv4/eJftQHOCj8=", "G2/Upye5U+Mk6PdpAXGqpb2qlwRyzfaaDHgWkhI08Qk=", "9sca06M4QxaxJfAVIB+rsUjCv3gxGfe7GFRygw42KKY=", "AlMp4Th/CWgkhwPt6tcQ7/QfjL7FuVUc4zJ8Lo3RtyU=", "C+qLMPgRsKlFWUHsVAz2hoMqK+gUHZgkN6Jqu2ROC3A=", "HVZEmiyx4pmbgjW9QXAKKP197TrfSVHa85pZY6sDD5o=", "MHBWepus+zwjAw8ry4zrnv+cqXf05fVqL0xQ5chNCf8=", "vab3ZNvp8kjhiOp9lbdbFKMtU/nNKQ8FmcLeAyfIB+c=", "zGqhV4FCdxiUBFet8CRw+hBnl705THTg4KevjDD1+gU=", "jSxpPMKI2cNfKxc8KkQKgcxLYugzOnESyMh5SaloM8U=", "S5Ayao/LOZVCkyizOMXuNutHxhWXGWSbYwcB0vCYBM4=", "UByZGnKC5kJI130KpKrLf9kDAlueKxmf5cqSWJvz+3M="], "CachedAssets": {"2fXJhy1TpFde0mPyAhr9n/IsE2gyYh7vOszKbTs2G3o=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\dark-theme.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/dark-theme#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4uuht94c2i", "Integrity": "XPcEZNDtRfNAktIH1FRUkD7lWNeALdB27Shlfltp99o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dark-theme.css", "FileLength": 9359, "LastWriteTime": "2025-07-18T14:55:41.051214+00:00"}, "49GQXRbC1u/3zXttpF0M+EsgSuunWD5HSlM0W9Ll9yE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\favicon.ico", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-17T17:27:49.7369237+00:00"}, "W72E8vEJPAFD1ToDN33+2NBvgQ9zrJu6Ljd3nCfj+ik=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\js\\site.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-17T17:27:49.8084766+00:00"}, "R7PAKo20yyqxanXCvJrTJqUIMn0sbRR66bRrLjJVRUs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-17T17:27:49.5655796+00:00"}, "jgtGZGofNSMGMa31DbGvsIAzuvtOe8yoqq7wD8PV1UA=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-17T17:27:49.5665804+00:00"}, "rFz1CHCHQQtYvUKIPcHI/LOy0+hVWN37TlJ/gW4O3WI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-17T17:27:49.5670854+00:00"}, "luoGfjdRQw5YOWyfK6VX1zsj6AN7WdejwIY6/Wqj0rU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-17T17:27:49.5670854+00:00"}, "yg7pJRuNadYqKM23npWh/CaFBvXT4R1y8obB2snEE18=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-17T17:27:49.5683259+00:00"}, "WikkFaObNia+HRrcHc3LjcNtHmkyQZmZFsKsy6NU2Bg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-17T17:27:49.570841+00:00"}, "16iVAqTtMzq7Gnza59iYZhIrF+7sHQ7lcKCn43b2NFI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-17T17:27:49.5748474+00:00"}, "7HBEjKiOONnOcQ7aVWkkammvNi17HzDjbpnJd/fwSqs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-17T17:27:49.5837782+00:00"}, "da4adgL7OvhhKXuZJRvx2A0FU1YpYYUUBtUhMlqqJPQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-17T17:27:49.5923013+00:00"}, "yptICzWMSa+07wOxJuywWQ5uxu0yomADuJhCS+k3y7E=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-17T17:27:49.5933002+00:00"}, "Ssxy7Ruu5tAPzd6TZu++vYOq078VePGBTWtHmQxtrBE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-17T17:27:49.5933002+00:00"}, "zKEJTKyV8qtAiBlNdC5TLGHIX2cIy7PGeZMGMy2+liA=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-17T17:27:49.5943012+00:00"}, "EY3Kza51WBHLYS1Wzunr1AAL5FzhZ+O6cZDahb9Dw6c=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-17T17:27:49.5943012+00:00"}, "nLoZNMdYmNby5HhG+8fArHUXlwXrLOnr88vh3Y6gU1U=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-17T17:27:49.5952999+00:00"}, "RBgidQmzt42sadbGC0MDOML510wSZ9ua9oTQK5o3xXE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-17T17:27:49.5952999+00:00"}, "f74lm5nqIXq3jIxa/TaVKhVeTmSkKOPaNWjl9nlFhjc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-17T17:27:49.5962996+00:00"}, "hH70RGwPUlePtfGntg3WhAR1crhTAt/gjLjG8Zc6nog=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-17T17:27:49.5972984+00:00"}, "sL0Gd/vbq71z2MI56mrL5/4HUv9G/gbhqQA/ndlLqf0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-17T17:27:49.5988055+00:00"}, "QO25HBa2HcjsMISSSB1xw6hcMGGikLsDHY9k1KElLOw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-17T17:27:49.5998121+00:00"}, "ONwztavErtsKeMXDlPWuqfnUSYCZEyq3qIh7ZeKf7J8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-17T17:27:49.5998121+00:00"}, "/P766NzCgNdPgCa4FkQgMJ2scSFFrSwaK+j4nOar7OM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-17T17:27:49.6020884+00:00"}, "KGwsGd+ItqfTYTEE2elExkcrP01/+3wtLKE/yHEyjYU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-17T17:27:49.6056046+00:00"}, "CdwiO55NVzhq5GJDNJgJ/NPS+pWdv7Vn1rtkdNhQcfc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-17T17:27:49.6066027+00:00"}, "t8sutfiFrf5WSr+zMVzyFjGD414jC2+rfhm5DDoAYVQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-17T17:27:49.6076035+00:00"}, "Cz7sns+uSSfTUPJ9kPyGI/f79niY2UuqmZZ1Bf1uRDs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-17T17:27:49.6091091+00:00"}, "IBMgzlLWsEYqjkvjTZNbHZ7/ma497Yz52Y5EyMwUTpY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-17T17:27:49.610116+00:00"}, "mJuuGzdkj75g5gvZhNMdmRs0BX9CJde8qw0W0WDQaEw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-17T17:27:49.6111218+00:00"}, "clyo4q+3cxRbVsQIeKKBpK87QOLFv8b5yC9HHwysN20=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-17T17:27:49.6135905+00:00"}, "eIA+4gjb//b1VlVu+1KW57BFscWYXY/ruRithSZGTMk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-17T17:27:49.6145891+00:00"}, "avorWBGlAEnLLV+abN/MaLAiVZnazdwUv9tTb99Hx40=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-17T17:27:49.6195563+00:00"}, "1z9XoDhor8kjhqIkdpqh0XwU2BfPiQYWejsf3esroIQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-17T17:27:49.621554+00:00"}, "+cFYkXHSyeRCO7ZWpmoGZIu4Cik53Ub2BHodmTxXxBE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-17T17:27:49.6285594+00:00"}, "F5/UtgVLgJoVrNVIjB/QOna7aWB67RKnNFuwZEmfZOs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-17T17:27:49.6405968+00:00"}, "QFHo8IXxl0XIQm72+5EBlvZtk77bX9n12tlfDwVYVhU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-17T17:27:49.6426528+00:00"}, "+fko3tMf13UT7wEQxj1ckU/ANu9empWlY+vNAHXthFQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-17T17:27:49.6446567+00:00"}, "RjSW/E6dhXB/8iOkg0iAilBUR0LAwIOsr9tj4+mX/BE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-17T17:27:49.6466536+00:00"}, "w0wycFNYSFDSf7XqvDCxtIzhgxI5mK2bHBP8BbKHZhM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-17T17:27:49.6491593+00:00"}, "Mza7gGWJonDiMHgEtwaBwU0scBmPoB0oAFDvvGDrvoQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-17T17:27:49.6511665+00:00"}, "o3O3Uu+7IKPdSFQa5P2Knly1gFebQwIBKuaCkzDNdeY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-17T17:27:49.6521652+00:00"}, "/5lOtZvartputSkB4WbIGce3GBJCu1lB8lrXF7Rhago=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-17T17:27:49.6561684+00:00"}, "fOj5M8a7wTFVjbAQSi8/ZvV3Atqu/xj+IOmTsDedruk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-17T17:27:49.6571661+00:00"}, "IM2F3dZhp96ZsuYBr9gXRwXLArEo9lF6FrgZCz+xAV8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-17T17:27:49.6606784+00:00"}, "ilhgdB4n0rKldDIrmBK4YPiViZ88Pt+WTb5oEeZoGls=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-17T17:27:49.6616854+00:00"}, "90cFAjBqc61xfREx2a0TisyzElBdwRRn/8xB2pmFxwc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-17T17:27:49.6662533+00:00"}, "zlufR+YLJjVLanhqdYnQO0NRfNQtFJEOsSR/gWA0TG4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-17T17:27:49.5670854+00:00"}, "Jtk0GlIkg+WX2h8IUU17qC5ThSb+56flKlvRdWcW6H4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-17T17:27:49.7517904+00:00"}, "l8i4ydodlDXb/iAkzzvPzXqXkC/ehZ9kkqDwAIzcRbs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-17T17:27:49.7645032+00:00"}, "mCyPgIKsKS2IMexa2nP6d1v+xSpjD1ahvuItjzmDTOE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-17T17:27:49.5810352+00:00"}, "wqnrBdFQawZyh8dF3gXflYnkZD+8m6bpgjLg2Rc+62w=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-17T17:27:49.7348911+00:00"}, "f0XL+0YkFqEk3avYX0Eb6XFmHXDIOoRdVJaZQkOYzEU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-17T17:27:49.7369237+00:00"}, "3ZKxbnVesTlQ0o+3Hzec8xyri18P3wTD11iwj5IQhwc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-17T17:27:49.7369237+00:00"}, "b7REs2Fff3uPO0Hi1pcOkBM6DdDJ0gPkDJcqyuIgGio=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-17T17:27:49.7384314+00:00"}, "rxxPSsqF/NDxRAs2mO76LrfxojTEfXZFbB+tnDS0IUk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-17T17:27:49.570841+00:00"}, "we8G4LVKehAwA1JGrY+KD5IcqefOBVOddrJC+WVBFac=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-17T17:27:49.6703759+00:00"}, "f3vrDiKWFzcgwVLhfngB9VRvZHHgcH0b9PGtrUbMwOg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-17T17:27:49.6713841+00:00"}, "i3pSuWFMSVi8ldHOHFnDSdz4TcxOPIgygsCwD1YwTnQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-17T17:27:49.682008+00:00"}, "g47QnuRG60+3NmDHluY2DdCy3ZD7+PLvbBm5Q4ff5WY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-17T17:27:49.6878138+00:00"}, "7PbJVjBqBGGW8wPp3ysAx1HA6KWpKlrAipmkK0QglqU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-17T17:27:49.6943349+00:00"}, "0KozhU0S4hHKbI0KCwFMnk65UVysz1tEzwKibjGvDmg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-17T17:27:49.7315779+00:00"}, "DeRVbWJc2/Qxwt+pvkIyMgaV1/L9jw4jxcOBE5CAw7c=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-17T17:27:49.5688328+00:00"}, "TCPtnki7ov6WFMzZzswWE4xoUfb0BkY8mfU9IyQZPz8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\petcare-animations.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/petcare-animations#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aulzgni<PERSON>n", "Integrity": "UkVGir3G/cVo6FIgopP4Zs215GAVARyMTIgk9vUalEw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\petcare-animations.css", "FileLength": 9072, "LastWriteTime": "2025-07-18T12:16:25.3000749+00:00"}, "rK2Z0YIGU3lHQQrxIj6YJZVaFFxKFsA4wklPjSdXytI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\site.css", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "debp5faei5", "Integrity": "9eYVCUhIVXRO5RPcdQgcf/Neu8ZriY+6VWmJS9sRapI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 17114, "LastWriteTime": "2025-07-18T00:53:48.7934284+00:00"}}, "CachedCopyCandidates": {}}