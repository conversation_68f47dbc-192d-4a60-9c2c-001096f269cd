﻿@model IEnumerable<PetCareWebsite.Models.Service>
@{
    ViewData["Title"] = "Trang Chủ";
    var serviceImages = new[] {
        "https://images.unsplash.com/photo-*************-f3b2795255f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Khám tổng quát
        "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Tiêm phòng
        "https://images.unsplash.com/photo-**********-03cce0bbc87b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Tắm rửa
        "https://images.unsplash.com/photo-1415369629372-26f2fe60c467?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Cắt tỉa lông
        "https://images.unsplash.com/photo-1551717743-49959800b1f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Chăm sóc răng
        "https://images.unsplash.com/photo-1576201836106-db1758fd1c97?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Xét nghiệm
        "https://images.unsplash.com/photo-1587300003388-59208cc962cb?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Chăm sóc tại nhà
        "https://images.unsplash.com/photo-1559190394-df5a28aab5c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Cấp cứu
        "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Điều trị bệnh
        "https://images.unsplash.com/photo-1628009368231-7bb7cfcb0def?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Tư vấn dinh dưỡng
        "https://images.unsplash.com/photo-1601758125946-6ec2ef64daf8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80", // Huấn luyện
        "https://images.unsplash.com/photo-1583512603805-3cc6b41f3edb?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"  // Khám định kỳ
    };
    var gradients = new[] { "bg-gradient-primary", "bg-gradient-success", "bg-gradient-warning", "bg-gradient-info", "bg-gradient-cute", "bg-gradient-pet" };
}

<!-- Hero Section -->
<div class="hero-section text-white py-5 mb-5 rounded-4 glass-effect">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 slide-in-left">
                <h1 class="display-4 fw-bold mb-4 floating">🐾 Chăm Sóc Thú Cưng Tận Tâm</h1>
                <p class="lead mb-4 slide-in-up">
                    Trung tâm chăm sóc thú cưng chuyên nghiệp với đội ngũ bác sĩ thú y giàu kinh nghiệm.
                    Chúng tôi cung cấp dịch vụ chăm sóc sức khỏe toàn diện cho những người bạn bốn chân của bạn.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="@Url.Action("Index", "Services")" class="btn btn-light btn-lg pulse glow">
                        <span class="bounce-heart">💖</span> Xem Dịch Vụ
                    </a>
                    @if (User.Identity?.IsAuthenticated != true)
                    {
                        <a href="@Url.Action("Register", "Account")" class="btn btn-outline-light btn-lg wiggle">
                            🎉 Đăng Ký Ngay
                        </a>
                    }
                </div>
            </div>
            <div class="col-lg-6 text-center slide-in-right">
                <div class="hero-image-container">
                    <!-- Professional Pet Care Image -->
                    <div class="hero-main-image">
                        <img src="https://images.unsplash.com/photo-*************-f3b2795255f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                             alt="Veterinarian caring for pets"
                             class="img-fluid rounded-4 shadow-lg floating"
                             style="max-height: 400px; object-fit: cover;">
                    </div>

                    <!-- Floating Service Icons -->
                    <div class="service-icon icon-1 floating-delayed">
                        <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                             alt="Pet grooming" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                    </div>
                    <div class="service-icon icon-2 floating">
                        <img src="https://images.unsplash.com/photo-**********-03cce0bbc87b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                             alt="Pet health check" class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                    </div>
                    <div class="service-icon icon-3 floating-delayed">
                        <img src="https://images.unsplash.com/photo-1415369629372-26f2fe60c467?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                             alt="Pet vaccination" class="rounded-circle" style="width: 45px; height: 45px; object-fit: cover;">
                    </div>

                    <!-- Decorative Elements -->
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container mb-5">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 fw-bold gradient-text mb-3">🌟 Tại Sao Chọn Chúng Tôi?</h2>
            <p class="lead text-muted">Chúng tôi cam kết mang đến dịch vụ chăm sóc thú cưng tốt nhất</p>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px;">
                        <span style="font-size: 2rem;">👨‍⚕️</span>
                    </div>
                    <h5 class="card-title gradient-text">Bác Sĩ Chuyên Nghiệp</h5>
                    <p class="card-text text-muted">Đội ngũ bác sĩ thú y giàu kinh nghiệm, được đào tạo bài bản</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px; animation-delay: 0.5s;">
                        <span style="font-size: 2rem;">⏰</span>
                    </div>
                    <h5 class="card-title gradient-text">Phục Vụ 24/7</h5>
                    <p class="card-text text-muted">Luôn sẵn sàng phục vụ trong các trường hợp khẩn cấp</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px; animation-delay: 1s;">
                        <span style="font-size: 2rem;">🏆</span>
                    </div>
                    <h5 class="card-title gradient-text">Chất Lượng Cao</h5>
                    <p class="card-text text-muted">Sử dụng thiết bị hiện đại và phương pháp điều trị tiên tiến</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Services -->
<div class="container mb-5">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 fw-bold gradient-text mb-3">💖 Dịch Vụ Nổi Bật</h2>
            <p class="lead text-muted">Các dịch vụ chăm sóc thú cưng được yêu thích nhất</p>
        </div>
    </div>
    <div class="row g-4">
        @{
            var serviceEmojis = new[] { "🛁", "✂️", "🩺", "💉", "🧖‍♀️", "🦷" };
            var gradients = new[] { "bg-gradient-primary", "bg-gradient-success", "bg-gradient-warning", "bg-gradient-info", "bg-gradient-cute", "bg-gradient-pet" };
        }
        @foreach (var (service, index) in Model.Take(6).Select((s, i) => (s, i)))
        {
            <div class="col-lg-4 col-md-6 slide-in-up" style="animation-delay: @(index * 0.1)s;">
                <div class="card h-100 border-0 shadow-sm service-card hover-lift hover-glow">
                    <div class="card-img-top position-relative overflow-hidden" style="height: 200px;">
                        <img src="@serviceImages[index % serviceImages.Length]"
                             alt="@service.Name"
                             class="w-100 h-100 object-fit-cover">
                        <div class="position-absolute top-0 start-0 w-100 h-100 @gradients[index % gradients.Length] d-flex align-items-center justify-content-center"
                             style="background: linear-gradient(45deg, rgba(0,0,0,0.6), rgba(0,0,0,0.3));">
                            <h6 class="text-white fw-bold text-center m-0">@service.Name</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title gradient-text">@service.Name</h5>
                        <p class="card-text text-muted">@service.Description.Substring(0, Math.Min(100, service.Description.Length))...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 text-success mb-0 fw-bold">@service.Price.ToString("N0") VNĐ</span>
                            <small class="text-muted"><i class="fas fa-clock me-1"></i>@service.DurationMinutes phút</small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="@Url.Action("Details", "Services", new { id = service.Id })" class="btn btn-primary w-100 glow">
                            <i class="fas fa-info-circle me-2"></i>Xem Chi Tiết
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>
    <div class="text-center mt-4">
        <a href="@Url.Action("Index", "Services")" class="btn btn-outline-primary btn-lg pulse">
            <i class="fas fa-th-large me-2"></i>Xem Tất Cả Dịch Vụ
        </a>
    </div>
</div>

<style>
/* Page specific enhancements */
.hero-section {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2d3748 100%) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Hero Image Styling */
.hero-image-container {
    position: relative;
    height: 450px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-main-image {
    position: relative;
    z-index: 2;
}

.hero-main-image img {
    border: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.hero-main-image img:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Service Icons */
.service-icon {
    position: absolute;
    z-index: 3;
    border-radius: 50%;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.service-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
}

.service-icon img {
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.icon-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.icon-2 {
    top: 60%;
    left: 5%;
    animation-delay: 1s;
}

.icon-3 {
    bottom: 15%;
    right: 20%;
    animation-delay: 2s;
}

/* Sparkle effects */
.sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #63b3ed;
    border-radius: 50%;
    animation: sparkle 3s ease-in-out infinite;
}

.sparkle:nth-child(1) {
    top: 10%;
    left: 15%;
    animation-delay: 0s;
}

.sparkle:nth-child(2) {
    top: 30%;
    right: 10%;
    animation-delay: 1s;
}

.sparkle:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 2s;
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Floating animations */
.floating {
    animation: float 6s ease-in-out infinite;
}

.floating-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 3s;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0px); }
}

/* Service Card Image Styling */
.service-card .card-img-top img {
    transition: all 0.3s ease;
    object-fit: cover;
}

.service-card:hover .card-img-top img {
    transform: scale(1.05);
}

.object-fit-cover {
    object-fit: cover !important;
}

/* Professional styling for service cards */
.service-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}
</style>
