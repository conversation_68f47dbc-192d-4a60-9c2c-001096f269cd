{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["ojPrRGl2QgoQ7RPpua5uZz+RlBBwTicuw+UXWYHv0D8=", "u6O6v3KTpxiYFsdpz4gUDpyEhRxEOtmqk7ZnCzkyaAo=", "YrNq01u41Lp4tNxnIRUZ4/UQD2+MuFXnE3OMRDEVwho=", "Z9AfQg5CRqApYG98xt/f/IUTfVVCYirzigPdIGXCwuI=", "T72Wi37naMobh9yFLffQtZDCrM4vC4H1ZJl3YkZKpP0=", "BJYoKL3vj7USNenamY6IcTsGQe00yY2HZsaU6bSiPTk=", "ysCrA+OarWtB0CyS98aX6sFLk+yvPRfUuE4MMbCVLjY=", "gr+z0BfSSPnKhccI8UwN7bg94aFZ7rZRjAhV7+gdkAc=", "fSPFnsk2ZvhNtUCGtQXx1XdaEhIkNIt0Gm4JsnTkd4g=", "6G5kCMnfozS3B/ciX283Gy6oRY1LHkQXhzPt4HNvkGE=", "xNxTgYu63OodNU2U52n+/HcBYg9axpf2rlgnN2N6Z7E=", "thPQcwr4dC8/pJOGQTQCR9IbP3krO/oQf3X/7BWl7m0=", "PJiQY+hgYcXe7kro+d8cCt/0ikGN4TnSEWgFQihmVC4=", "3eIx86jctTp4/zJyJPdB8ySNyZenQY804QJzU2wgoBU=", "us620VoVVUzRxM4r5133BNJgLS03tmHPALgY5zyMUes=", "hCa/lpv72zGR8l2RB6QQ48O7tjvV/hwVk3x7HvGX5VU=", "BqAbxF58pDN12bSYC3iXbxL0ubD0iy2YHklZGZzrJt8=", "5DrS9ZrHV6t2qvLnexIHvc8p2LvecAuSczjR4Iu907Y=", "OUzICal6wU8PuzQUyznygdvhuLm7OWb1ZVNMDbcsNoc=", "j6L8YgBm4qIMv3Oo1a/jDDAMxIiSArA6epdD9otX0/0=", "mv6jstRNxGaJQlWDcopHlDSOh0BOJbfFdeEkMlDdNL0=", "iG22sHOvVwt9dA26/6r45o6fVcUrdsjjRpXmAAcIMb4=", "WxuQXinZCbqxyI9Q8nPOyMewUFDi4m/Y6Tgjc1BRb8c=", "AYZibxch6alNdT/7RJr4s+S7AFY1XbFY8ZT1jDeLJXI=", "OgHj28vHM38DSejbKOYCTfvfGbMia23dzW4kRdWRv+E=", "V2w5qhmsIQWbgPteLrgehSu0uruJkiLdiKYn33PMUV0=", "QjyLQShOL2Df5muiJVQw6ZjRYa9OTsOnreBQM+ENZb0=", "yzECVeCragqfDLTTFS0n0olPXsogD445EKom2No/ed0=", "bH8g26T8kf+6la1GxuPqQ3oWcAuABBcCOHXKaw3mHMk=", "++E<PERSON><PERSON><PERSON>ovOcGH0i0PEsIhsfYKBfXSvYJIIBLiRszXUp4=", "+29BbJMzptZn5kO8DjQCGOZFPGSjT7Gh50OrJteEGCU=", "u2c5VKomWlxjnaTTJYvKUTs23b3pblZStwkIA76E/5A=", "bK6Yx07qN0/NW9mCsteViN4tISvCkWBSfkYPpkJ6LjY=", "qxFDyQo0fBVcqpgRj71N+OLrxxXrcNXtc23O8xgGGXA=", "oFghsfGuAJFcL2UOtC0d6gDUWwytGYMKPrAk8KR5F/E=", "qXyg5BKCe51P5GsRw+QzMq54quJSqvwvKyiT/u9cUBc=", "kVlkFPZDdSuwfqPTvQ0Y3QL99FCOYl41rwFZlLo75VI=", "INunoE77n6OKgkE5vV+xdMzfoLpGcFM9NZyLN12ZIlA=", "cM5ONdAd9kqXKLKwoTtXVqFr/D3FC2/OYRffInRkhs4=", "hMisyIPBTwIV3y0OaurKYnPGmTGNLrNVXQrIPCAkzwM=", "0GzI4wyyM4X12H/UjxRs4hntEyGqpRbJqw2KeY1qzhk=", "PaIEiwnueoJpjb7Yp5UetWsZT75CVvd0YbNwvYae1p0=", "6cwdOipkFlhYKTWqRz4Gjr63zJm8mZZlEfgvNPL0NRs=", "pQRRSgC6qxZwNW49Rr25hpfT43jDLvkhrZgXTcxE+/o=", "TSUwpBjYPvqZk9p88GjQme+NABEx6FIOk0b66aPjRU0=", "Ww4u0p5zUPzX9iDQiexW9v15xnGsTH9XM65vTtXb8p0=", "F+q/lqAdXiP75OoDk2FNAQRzmJtwlVnRYjpm+QErYLo=", "5kjCBEXUxRlIEZTGihkCRjeQxJ5bvJz83nrfCqUfCRQ=", "CWRxgXe4SGoKeG5JLWAFKgDj3KNwnYCZL+XaBPFwOhg=", "zYEIYGBpjetMXFuZPrGzma4YtpWi6N/NibRzO4c4rl4=", "bPXdcVVVyvGSuvdSC38QDPDOe2efrbjoruChxe9MFg0=", "Bbh0oc453+fh/ofCubEg8QdHhG8zwfy5baHSLja2AyY=", "96QW1aH/z0T1cOKioa+qmejI9LM8sKkV+Kvof9t+sjQ=", "ono/NY6coP/1rAEfIo4pQW7xuuh/NUcS1RhHEpRROLs=", "0qKnrhjOhV62W/6pIff/KKbgp+il/YE80wZTht0epfA=", "HFwgQ+BcHuD4BsgQ8TujQyOFWZ7Hku0Z5k3tK+cuwdE=", "tJBVOjNw/Fx3Isly9faCiY4S8De/mFYq9PXlSTmUCpw=", "+irehWbacIWV7Qxzfld9kM/S2CGRqMKmjTsX0x9nRnw=", "M3Q8ESgORElOYGYeH4rhraec+r0x53FbovSrCl68aCE=", "+3Gk+HmclzsLhv/z0fCHPDi2ILnVGuHwsDt4oSiBlRk=", "xLT6/qtseQQ/odgqyt0Jj1aOAcAtUKY8Hc98nGiccdY=", "J6SLKmygOc+rD6RoRPtdetaeO4YdHl28t4JyU0QHrCE=", "hPaHL0Tqqt+1IgoZzuKb3ezjfdYGN3MoilEwQ2QGZ3o=", "bMkikATF93T8gIrJoxnGtKtpzK1YyGVy0fAssOjvcZ4=", "0ful1CDtYqXOz8znnJw5ifmkkLOwkHn0/ColK0/syqQ=", "FwzIMzEIrAq56n99cXlcCHLcZm3WyVpwGfW9cQQ3/mQ=", "/H2uTkiV75LUVkZzdQbbRZrOFXGiv9N4c9UAPi67/6Y=", "lQ0H1+ARfRNSOmKLIT96t9wqBKmC5GOL8vuXcfMffYg=", "I+uH3UmMIaKQwBFiTwbHHpMvK+Mgk7ijNfdjHivOCYQ=", "95a7PQYbsEjmUV9yxA4YH/yjd9w/oGbdwSlJpcN5N9k=", "Egj1jQNhRBSD2R86QVk1F4RQU9l2PKWYWq+olUy9qLg=", "WmyGv1ENQiZvYv0RvjBmIjSsaxtxwjGowxvdEw3sYcI=", "BcrUccM2PY5bYMsgCRL9MSo71J0ObEb014Od8iSvCAw=", "grvEIOLIRQcZlO1XCTet7r3PWdCNx1l/g7yCAws++A8=", "IZzKICBJgjDDlI15rWXOl2x313QbOwv8PCBnlHrAW4U=", "xA2TChsAnO5bNr2pCbB817XJu8IrN5aEKFabSmcp8IU=", "7ZbIeybj/fi954ZAExnnUI3t5gjzJfwXAq8UWX+iIZ8=", "o/ULttd9TXwtBvVbNcUPNmvfXFU+cOJ9LborHauhrLY=", "KBL282Tgiujr6rxds0UatE7Ez/wxRLmPoGnTYKEUToU=", "PYWwZ+MMd3ehOBTkWYnHrPUD8bo/goFYinhnLlgO3k8=", "MrqFuXfX6Zxd56JV//9XGMsXFlLzUzT52qGdi5304XU=", "gdNaH1SBw+bNJ2PFKDQGxDHuXyEH17ry6uUvwfHok9k=", "NYB8iXvRq/3AZvL1kLUZtmsystmnMOhKNFIgbr8OIo4=", "uwemGoPV2OthVjOLtCXJtmYfDK4b1ThFKgTAshTuX2c=", "dfcEEPxJLkz/T53qZ8fbhm/mXDOlR9FtEK/MzcUCc/s=", "AF2r6uP00rfcj8MwMWCagnuSPOUooDOc6t5sZk1nwA0=", "feYUk5WDRaLUaz7h6YyNmNRfb/I9ddKmF7hkGQqT67o=", "UKT7ZlEydZvSIqCiFro9MTNTIzt7BmbAxF4cc0SPkvs=", "QLu7BT0xeT9Z1MOgIdVZ8oPMxxJQvXJ1CkaiaDf8ArI=", "EJzNPcr1JKD2myFCFWh0xmsITh4xUsC14JdCrl/QJRE=", "J2bBJ2YqIKK/GY3JBWkgPd22qy6+h0huDSbhTFw5JDw=", "qEFwit8GKfpLJlWO3rIzw/aWedwSIFreTOH+0HejAYg=", "t6QrrF59uCaNqoG6DRbhVB3LL+ndsDHZwekSoH/y7sk=", "C6NL9q5tZ/kbM0Lnmxf7EOnBEogwUPF0kFlTLLP7FpM=", "i6xpUmI6DyuA06oPnvcLc+sR0w9m6q9iCskljz0/GKI=", "7T9xE//k5udu2xx2CuS+CWNRQ0cevcyiPcCIfRNq3HQ=", "p6hwSwCo39uvgGCeq+J+33mhbvIjM9T7zcc7l+9Vuhk=", "kyCODvEkVfU9/RmUr424hj69l9qxjBT542dFLfPjY/A=", "5WjAIG8Do0XFz5+i/NdE5Ho1oHgePo9PGAY7DJb4RX0=", "vKb2wCqyWjw7q3g5dp3kD0MAExmr4M2zJHaT9BSlKPc=", "ZWp/r/iAv6QsUEhpaMSpVe1a2DVcicunt5Kgi3v1EZw=", "BnpWHlJnOtfbOGr6xRan+M7yqnGbXc59idiZHx1/E5I=", "EIJNO9OBzXJHUyV8ke4+OE/kdTn3bMED/poaXR2GksM=", "RJ7Da8JXAwBaeL/lsSiu9GBtIyoSslsSbNuV/SpyLfI=", "XZaZ59PLyA+qmiUnDC1tcTgrWcd5ddF8pvwPkVVfsnI=", "IV9PWaI7AKQaQ7D043Y+ThRtdndSsuwPW5eZ4uRLhd0=", "qUQuKbP3VH7iSsdM6qsvnsCTA8NfZltEcoXJ1xv6z34=", "2ZS2I8opUVQtGZRBW1cPGQxzFRkBlMr+BLLM6dNzwfc=", "N9HBugCxCPRWYsl0AwwPfoDEcBshd4KDWnOEtFoSBY8=", "XqM1DQyDf1vkaTsO7P4Nw5r6UWRb+am64zawM+Gh9f0=", "1cou/iRTh0iZMYfBexlnz/OmtqmOFJ+q5AzGFIgMMg4=", "L9ZOYOYDWfpuB3rbCFEgppof6cbawSqQuBkyYXGzja0=", "YxLNmpD70jWHeroIvqXQ91ajIgG8UhraHFShWdy5PG4=", "O0RJw0CaYSIcKu8ddYZTFM9vSD6vGl7mSr3VC/yHoR8=", "wkjdNYf/UC0gygdCvmb6JOFIIajcnHZytpIsRLY8XGw=", "V9SQOJGVMQCCuLnReO8IlFix2lykUVTAxQ5Wz9PxUAo=", "rPpNcpXzt2MzmBwhavqjQD7mMcYN6lzJ+FfXQdlNt7w=", "kLRv190X4WefAhwuz8dsdw2xFkEH9KzEFrGdpVhn9gM=", "ezdqbeKcbUKcXY8z2tFpD8WfpJyq7b2TFM5kgzwUxmM=", "j0kaEP35DGPPNkY2lvkoq6v5yYiesGRJZODjbVazbBg=", "FLfYs8+EImMIhDjFGQPz6INQdBUxXHXw3cScrkI/Vfo=", "Lfs/OfKVbrkBRM5u/Fzm2KwbWvbLWRa6HnDvw2juN5s=", "ghzMpX1W8rVkS/bB9U+ZhVCliXYZKfHRxLBbmU21FtI=", "SPqoWw3oUtVW3CQCQ/SU5SUT6hBj0M3c7yvUWJsEG08=", "kNVL7polm1TEogzo4jCFYz1XFwLMzpJ6krgq8275P8Q=", "3RBs5wvagQZwI7jqDpT7MTNq9QM0Qn0hF03zE7pvwoE=", "yU5W+nk1WE7Tu5E6HxqYp7notJ+hKu2dnjTZIHAZEcU=", "LFeO0ZvRNumQWnOkWLjqu9KI8+EWyQdplKWEpC8aaCk="], "CachedAssets": {"ojPrRGl2QgoQ7RPpua5uZz+RlBBwTicuw+UXWYHv0D8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-17T17:31:37.3314256+00:00"}, "u6O6v3KTpxiYFsdpz4gUDpyEhRxEOtmqk7ZnCzkyaAo=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-17T17:31:37.3396614+00:00"}, "YrNq01u41Lp4tNxnIRUZ4/UQD2+MuFXnE3OMRDEVwho=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-17T17:31:37.3466737+00:00"}, "Z9AfQg5CRqApYG98xt/f/IUTfVVCYirzigPdIGXCwuI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-17T17:31:37.3588618+00:00"}, "T72Wi37naMobh9yFLffQtZDCrM4vC4H1ZJl3YkZKpP0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-17T17:31:37.3629891+00:00"}, "BJYoKL3vj7USNenamY6IcTsGQe00yY2HZsaU6bSiPTk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-17T17:31:37.3659885+00:00"}, "ysCrA+OarWtB0CyS98aX6sFLk+yvPRfUuE4MMbCVLjY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-17T17:31:37.3725112+00:00"}, "gr+z0BfSSPnKhccI8UwN7bg94aFZ7rZRjAhV7+gdkAc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-17T17:31:37.3705043+00:00"}, "fSPFnsk2ZvhNtUCGtQXx1XdaEhIkNIt0Gm4JsnTkd4g=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-17T17:31:37.3785097+00:00"}, "6G5kCMnfozS3B/ciX283Gy6oRY1LHkQXhzPt4HNvkGE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-17T17:31:37.3800202+00:00"}, "xNxTgYu63OodNU2U52n+/HcBYg9axpf2rlgnN2N6Z7E=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-17T17:31:37.3396614+00:00"}, "thPQcwr4dC8/pJOGQTQCR9IbP3krO/oQf3X/7BWl7m0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-17T17:31:37.3456731+00:00"}, "PJiQY+hgYcXe7kro+d8cCt/0ikGN4TnSEWgFQihmVC4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-17T17:31:37.3599786+00:00"}, "3eIx86jctTp4/zJyJPdB8ySNyZenQY804QJzU2wgoBU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-17T17:31:37.3629891+00:00"}, "us620VoVVUzRxM4r5133BNJgLS03tmHPALgY5zyMUes=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-17T17:31:37.3649895+00:00"}, "hCa/lpv72zGR8l2RB6QQ48O7tjvV/hwVk3x7HvGX5VU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-17T17:31:37.3775123+00:00"}, "BqAbxF58pDN12bSYC3iXbxL0ubD0iy2YHklZGZzrJt8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-17T17:31:37.3860249+00:00"}, "5DrS9ZrHV6t2qvLnexIHvc8p2LvecAuSczjR4Iu907Y=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-17T17:31:37.4145952+00:00"}, "OUzICal6wU8PuzQUyznygdvhuLm7OWb1ZVNMDbcsNoc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-17T17:31:37.4175955+00:00"}, "j6L8YgBm4qIMv3Oo1a/jDDAMxIiSArA6epdD9otX0/0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-17T17:31:37.4201098+00:00"}, "mv6jstRNxGaJQlWDcopHlDSOh0BOJbfFdeEkMlDdNL0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-17T17:31:37.3436729+00:00"}, "iG22sHOvVwt9dA26/6r45o6fVcUrdsjjRpXmAAcIMb4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-17T17:31:37.3476732+00:00"}, "WxuQXinZCbqxyI9Q8nPOyMewUFDi4m/Y6Tgjc1BRb8c=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-17T17:31:37.3810263+00:00"}, "AYZibxch6alNdT/7RJr4s+S7AFY1XbFY8ZT1jDeLJXI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-17T17:31:37.3820269+00:00"}, "OgHj28vHM38DSejbKOYCTfvfGbMia23dzW4kRdWRv+E=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-17T17:31:37.4406491+00:00"}, "V2w5qhmsIQWbgPteLrgehSu0uruJkiLdiKYn33PMUV0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-17T17:31:37.445657+00:00"}, "QjyLQShOL2Df5muiJVQw6ZjRYa9OTsOnreBQM+ENZb0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-17T17:31:37.4501711+00:00"}, "yzECVeCragqfDLTTFS0n0olPXsogD445EKom2No/ed0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-17T17:31:37.4619401+00:00"}, "bH8g26T8kf+6la1GxuPqQ3oWcAuABBcCOHXKaw3mHMk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-17T17:31:37.4737625+00:00"}, "++EcKZovOcGH0i0PEsIhsfYKBfXSvYJIIBLiRszXUp4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-17T17:31:37.4777697+00:00"}, "+29BbJMzptZn5kO8DjQCGOZFPGSjT7Gh50OrJteEGCU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-17T17:31:37.3501896+00:00"}, "u2c5VKomWlxjnaTTJYvKUTs23b3pblZStwkIA76E/5A=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-17T17:31:37.3619872+00:00"}, "bK6Yx07qN0/NW9mCsteViN4tISvCkWBSfkYPpkJ6LjY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-17T17:31:37.3765107+00:00"}, "qxFDyQo0fBVcqpgRj71N+OLrxxXrcNXtc23O8xgGGXA=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-17T17:31:37.3850277+00:00"}, "oFghsfGuAJFcL2UOtC0d6gDUWwytGYMKPrAk8KR5F/E=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-17T17:31:37.4416565+00:00"}, "qXyg5BKCe51P5GsRw+QzMq54quJSqvwvKyiT/u9cUBc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-17T17:31:37.4694456+00:00"}, "kVlkFPZDdSuwfqPTvQ0Y3QL99FCOYl41rwFZlLo75VI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-17T17:31:37.4812879+00:00"}, "INunoE77n6OKgkE5vV+xdMzfoLpGcFM9NZyLN12ZIlA=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-17T17:31:37.3850277+00:00"}, "cM5ONdAd9kqXKLKwoTtXVqFr/D3FC2/OYRffInRkhs4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-17T17:31:37.3935467+00:00"}, "hMisyIPBTwIV3y0OaurKYnPGmTGNLrNVXQrIPCAkzwM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-17T17:31:37.3985468+00:00"}, "0GzI4wyyM4X12H/UjxRs4hntEyGqpRbJqw2KeY1qzhk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-17T17:31:37.3466737+00:00"}, "PaIEiwnueoJpjb7Yp5UetWsZT75CVvd0YbNwvYae1p0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-17T17:31:37.3619872+00:00"}, "6cwdOipkFlhYKTWqRz4Gjr63zJm8mZZlEfgvNPL0NRs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-17T17:31:37.3695053+00:00"}, "pQRRSgC6qxZwNW49Rr25hpfT43jDLvkhrZgXTcxE+/o=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-17T17:31:37.3735102+00:00"}, "TSUwpBjYPvqZk9p88GjQme+NABEx6FIOk0b66aPjRU0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-17T17:31:37.3810263+00:00"}, "Ww4u0p5zUPzX9iDQiexW9v15xnGsTH9XM65vTtXb8p0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-17T17:31:37.3830268+00:00"}, "F+q/lqAdXiP75OoDk2FNAQRzmJtwlVnRYjpm+QErYLo=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-17T17:31:37.3870254+00:00"}, "5kjCBEXUxRlIEZTGihkCRjeQxJ5bvJz83nrfCqUfCRQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-17T17:31:37.3850277+00:00"}, "CWRxgXe4SGoKeG5JLWAFKgDj3KNwnYCZL+XaBPFwOhg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-17T17:31:37.3860249+00:00"}, "zYEIYGBpjetMXFuZPrGzma4YtpWi6N/NibRzO4c4rl4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-17T17:31:37.3870254+00:00"}, "bPXdcVVVyvGSuvdSC38QDPDOe2efrbjoruChxe9MFg0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-17T17:31:37.3389706+00:00"}, "Bbh0oc453+fh/ofCubEg8QdHhG8zwfy5baHSLja2AyY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-17T17:31:37.3491801+00:00"}, "96QW1aH/z0T1cOKioa+qmejI9LM8sKkV+Kvof9t+sjQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-17T17:31:37.4135948+00:00"}, "ono/NY6coP/1rAEfIo4pQW7xuuh/NUcS1RhHEpRROLs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-17T17:31:37.3870254+00:00"}, "0qKnrhjOhV62W/6pIff/KKbgp+il/YE80wZTht0epfA=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-17T17:31:37.3885314+00:00"}, "HFwgQ+BcHuD4BsgQ8TujQyOFWZ7Hku0Z5k3tK+cuwdE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-17T17:31:37.3975457+00:00"}, "tJBVOjNw/Fx3Isly9faCiY4S8De/mFYq9PXlSTmUCpw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-17T17:31:37.4476552+00:00"}, "+irehWbacIWV7Qxzfld9kM/S2CGRqMKmjTsX0x9nRnw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-17T17:31:37.4531756+00:00"}, "M3Q8ESgORElOYGYeH4rhraec+r0x53FbovSrCl68aCE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-17T17:31:37.4609339+00:00"}, "+3Gk+HmclzsLhv/z0fCHPDi2ILnVGuHwsDt4oSiBlRk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-17T17:31:37.4639399+00:00"}, "xLT6/qtseQQ/odgqyt0Jj1aOAcAtUKY8Hc98nGiccdY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-17T17:31:37.3436729+00:00"}, "J6SLKmygOc+rD6RoRPtdetaeO4YdHl28t4JyU0QHrCE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-17T17:31:37.356345+00:00"}, "hPaHL0Tqqt+1IgoZzuKb3ezjfdYGN3MoilEwQ2QGZ3o=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\6ex06yhfvs-4uuht94c2i.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/dark-theme#[.{fingerprint=4uuht94c2i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\dark-theme.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "raeslh2o3h", "Integrity": "nY/h3U0Iqwr5Zo6ebbsVQsPcZYEhhLs5d0MRpgzGmh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\dark-theme.css", "FileLength": 1860, "LastWriteTime": "2025-07-25T00:42:58.8113687+00:00"}, "FwzIMzEIrAq56n99cXlcCHLcZm3WyVpwGfW9cQQ3/mQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\2umrnlcpli-61n19gt1b8.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-17T17:31:37.3599786+00:00"}, "/H2uTkiV75LUVkZzdQbbRZrOFXGiv9N4c9UAPi67/6Y=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\j25rawy20c-xtxxf3hu2r.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-17T17:31:37.3619872+00:00"}, "lQ0H1+ARfRNSOmKLIT96t9wqBKmC5GOL8vuXcfMffYg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\906i0a2q5e-bqjiyaj88i.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-17T17:31:37.3639897+00:00"}, "I+uH3UmMIaKQwBFiTwbHHpMvK+Mgk7ijNfdjHivOCYQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\wrjbr6vcde-c2jlpeoesf.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-17T17:31:37.3695053+00:00"}, "95a7PQYbsEjmUV9yxA4YH/yjd9w/oGbdwSlJpcN5N9k=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\47g2qcej9t-erw9l3u2r3.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-17T17:31:37.3705043+00:00"}, "Egj1jQNhRBSD2R86QVk1F4RQU9l2PKWYWq+olUy9qLg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\16xp4y96e4-aexeepp0ev.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-17T17:31:37.3735102+00:00"}, "WmyGv1ENQiZvYv0RvjBmIjSsaxtxwjGowxvdEw3sYcI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\tx0u35zzsu-d7shbmvgxk.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-17T17:31:37.37551+00:00"}, "BcrUccM2PY5bYMsgCRL9MSo71J0ObEb014Od8iSvCAw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\kinl620wdp-ausgxo2sd3.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-17T17:31:37.3416728+00:00"}, "grvEIOLIRQcZlO1XCTet7r3PWdCNx1l/g7yCAws++A8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\m2ocmpbwj3-k8d9w2qqmf.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-17T17:31:37.3456731+00:00"}, "IZzKICBJgjDDlI15rWXOl2x313QbOwv8PCBnlHrAW4U=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\0beionscbx-cosvhxvwiu.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-17T17:31:37.3588618+00:00"}, "xA2TChsAnO5bNr2pCbB817XJu8IrN5aEKFabSmcp8IU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\gbv9quotni-ub07r2b239.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-17T17:31:37.3609883+00:00"}, "7ZbIeybj/fi954ZAExnnUI3t5gjzJfwXAq8UWX+iIZ8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\jw1zpc57y0-fvhpjtyr6v.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-17T17:31:37.3659885+00:00"}, "o/ULttd9TXwtBvVbNcUPNmvfXFU+cOJ9LborHauhrLY=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\f3l3wowxh5-b7pk76d08c.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-17T17:31:37.3695053+00:00"}, "KBL282Tgiujr6rxds0UatE7Ez/wxRLmPoGnTYKEUToU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\22zygtcjb1-fsbi9cje9m.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-17T17:31:37.3735102+00:00"}, "PYWwZ+MMd3ehOBTkWYnHrPUD8bo/goFYinhnLlgO3k8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\719de5ie0m-rzd6atqjts.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-17T17:31:37.3735102+00:00"}, "MrqFuXfX6Zxd56JV//9XGMsXFlLzUzT52qGdi5304XU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\3jriwpskje-ee0r1s7dh0.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-17T17:31:37.3765107+00:00"}, "gdNaH1SBw+bNJ2PFKDQGxDHuXyEH17ry6uUvwfHok9k=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\r4xu2yj76l-dxx9fxp4il.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-17T17:31:37.3775123+00:00"}, "NYB8iXvRq/3AZvL1kLUZtmsystmnMOhKNFIgbr8OIo4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\fyx0kztiln-jd9uben2k1.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-17T17:31:37.3324247+00:00"}, "uwemGoPV2OthVjOLtCXJtmYfDK4b1ThFKgTAshTuX2c=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ju7hqlh2oh-khv3u5hwcm.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-17T17:31:37.3466737+00:00"}, "dfcEEPxJLkz/T53qZ8fbhm/mXDOlR9FtEK/MzcUCc/s=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ol6c0k6zx4-r4e9w2rdcm.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-17T17:31:37.437137+00:00"}, "AF2r6uP00rfcj8MwMWCagnuSPOUooDOc6t5sZk1nwA0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\p59fcj4fb7-lcd1t2u6c8.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-17T17:31:37.3775123+00:00"}, "feYUk5WDRaLUaz7h6YyNmNRfb/I9ddKmF7hkGQqT67o=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\xisn4hubse-c2oey78nd0.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-17T17:31:37.3810263+00:00"}, "UKT7ZlEydZvSIqCiFro9MTNTIzt7BmbAxF4cc0SPkvs=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\svt159khaz-tdbxkamptv.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-17T17:31:37.3830268+00:00"}, "QLu7BT0xeT9Z1MOgIdVZ8oPMxxJQvXJ1CkaiaDf8ArI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\3d6xx42ecr-j5mq2jizvt.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-17T17:31:37.3870254+00:00"}, "EJzNPcr1JKD2myFCFWh0xmsITh4xUsC14JdCrl/QJRE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\0wpl5jbpzl-06098lyss8.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-17T17:31:37.3955469+00:00"}, "J2bBJ2YqIKK/GY3JBWkgPd22qy6+h0huDSbhTFw5JDw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\cd04xlzlg2-nvvlpmu67g.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-17T17:31:37.4070665+00:00"}, "qEFwit8GKfpLJlWO3rIzw/aWedwSIFreTOH+0HejAYg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\xplglxmzrk-s35ty4nyc5.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-17T17:31:37.4115966+00:00"}, "t6QrrF59uCaNqoG6DRbhVB3LL+ndsDHZwekSoH/y7sk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ioti6b1z7d-pj5nd1wqec.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-17T17:31:37.3541887+00:00"}, "C6NL9q5tZ/kbM0Lnmxf7EOnBEogwUPF0kFlTLLP7FpM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\np2qbl6czx-46ein0sx1k.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-17T17:31:37.3765107+00:00"}, "i6xpUmI6DyuA06oPnvcLc+sR0w9m6q9iCskljz0/GKI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\o5dgjljmwr-v0zj4ognzu.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-17T17:31:37.4326298+00:00"}, "7T9xE//k5udu2xx2CuS+CWNRQ0cevcyiPcCIfRNq3HQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ibdw10w2e0-37tfw0ft22.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-17T17:31:37.3830268+00:00"}, "p6hwSwCo39uvgGCeq+J+33mhbvIjM9T7zcc7l+9Vuhk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ulkmceem45-hrwsygsryq.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-17T17:31:37.3990524+00:00"}, "kyCODvEkVfU9/RmUr424hj69l9qxjBT542dFLfPjY/A=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\iggd7zbp9z-pk9g2wxc8p.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-17T17:31:37.4085756+00:00"}, "5WjAIG8Do0XFz5+i/NdE5Ho1oHgePo9PGAY7DJb4RX0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\53yryp7xz9-ft3s53vfgj.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-17T17:31:37.4191001+00:00"}, "vKb2wCqyWjw7q3g5dp3kD0MAExmr4M2zJHaT9BSlKPc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\joae7a7cmj-6cfz1n2cew.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-17T17:31:37.4231094+00:00"}, "ZWp/r/iAv6QsUEhpaMSpVe1a2DVcicunt5Kgi3v1EZw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\5y0yfaw0le-6pdc2jztkx.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-17T17:31:37.4336311+00:00"}, "BnpWHlJnOtfbOGr6xRan+M7yqnGbXc59idiZHx1/E5I=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\hg2pqlatyj-493y06b0oq.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-17T17:31:37.438642+00:00"}, "EIJNO9OBzXJHUyV8ke4+OE/kdTn3bMED/poaXR2GksM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\jyublgxywx-iovd86k7lj.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-17T17:31:37.3466737+00:00"}, "RJ7Da8JXAwBaeL/lsSiu9GBtIyoSslsSbNuV/SpyLfI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\dnw5kp1xoe-vr1egmr9el.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-17T17:31:37.3619872+00:00"}, "XZaZ59PLyA+qmiUnDC1tcTgrWcd5ddF8pvwPkVVfsnI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\0c4s2juub1-kbrnm935zg.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-17T17:31:37.3715104+00:00"}, "IV9PWaI7AKQaQ7D043Y+ThRtdndSsuwPW5eZ4uRLhd0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\wkwtm6u1f9-jj8uyg4cgr.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-17T17:31:37.3745119+00:00"}, "qUQuKbP3VH7iSsdM6qsvnsCTA8NfZltEcoXJ1xv6z34=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\bb7gw5h3vf-y7v9cxd14o.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-17T17:31:37.3800202+00:00"}, "2ZS2I8opUVQtGZRBW1cPGQxzFRkBlMr+BLLM6dNzwfc=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\pfn94gxk9q-notf2xhcfb.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-17T17:31:37.3840269+00:00"}, "N9HBugCxCPRWYsl0AwwPfoDEcBshd4KDWnOEtFoSBY8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\7jaqs54623-h1s4sie4z3.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-17T17:31:37.4060653+00:00"}, "XqM1DQyDf1vkaTsO7P4Nw5r6UWRb+am64zawM+Gh9f0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\8y4g0n8xs0-63fj8s7r0e.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-17T17:31:37.3885314+00:00"}, "1cou/iRTh0iZMYfBexlnz/OmtqmOFJ+q5AzGFIgMMg4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\c87agy8a58-0j3bgjxly4.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-17T17:31:37.3945457+00:00"}, "L9ZOYOYDWfpuB3rbCFEgppof6cbawSqQuBkyYXGzja0=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\lkd4mtli9a-47otxtyo56.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-17T17:31:37.3955469+00:00"}, "YxLNmpD70jWHeroIvqXQ91ajIgG8UhraHFShWdy5PG4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\8xka8voxn1-4v8eqarkd7.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-17T17:31:37.3314256+00:00"}, "O0RJw0CaYSIcKu8ddYZTFM9vSD6vGl7mSr3VC/yHoR8=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\dzhmashnp4-356vix0kms.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-17T17:31:37.3324247+00:00"}, "wkjdNYf/UC0gygdCvmb6JOFIIajcnHZytpIsRLY8XGw=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\nv3ldcplqp-83jwlth58m.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-17T17:31:37.3486733+00:00"}, "V9SQOJGVMQCCuLnReO8IlFix2lykUVTAxQ5Wz9PxUAo=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\8z5aqhtjgo-mrlpezrjn3.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-17T17:31:37.3619872+00:00"}, "rPpNcpXzt2MzmBwhavqjQD7mMcYN6lzJ+FfXQdlNt7w=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\auvybedym1-lzl9nlhx6b.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-17T17:31:37.3639897+00:00"}, "kLRv190X4WefAhwuz8dsdw2xFkEH9KzEFrGdpVhn9gM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\odpnf6i3pw-ag7o75518u.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-17T17:31:37.3649895+00:00"}, "ezdqbeKcbUKcXY8z2tFpD8WfpJyq7b2TFM5kgzwUxmM=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\1c9ppmur29-x0q3zqp4vz.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-17T17:31:37.3669894+00:00"}, "j0kaEP35DGPPNkY2lvkoq6v5yYiesGRJZODjbVazbBg=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ky0nptvu3m-0i3buxo5is.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-17T17:31:37.3765107+00:00"}, "FLfYs8+EImMIhDjFGQPz6INQdBUxXHXw3cScrkI/Vfo=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\gm2edala4k-o1o13a6vjx.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-17T17:31:37.3800202+00:00"}, "Lfs/OfKVbrkBRM5u/Fzm2KwbWvbLWRa6HnDvw2juN5s=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\5wosomcsn6-ttgo8qnofa.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-17T17:31:37.3840269+00:00"}, "ghzMpX1W8rVkS/bB9U+ZhVCliXYZKfHRxLBbmU21FtI=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\aeftsib7o4-2z0ns9nrw6.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-17T17:31:37.3436729+00:00"}, "SPqoWw3oUtVW3CQCQ/SU5SUT6hBj0M3c7yvUWJsEG08=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\i14e9dte6c-muycvpuwrr.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-17T17:31:37.3476732+00:00"}, "kNVL7polm1TEogzo4jCFYz1XFwLMzpJ6krgq8275P8Q=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\x1ezd4a7vs-87fc7y1x7t.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-17T17:31:37.3649895+00:00"}, "3RBs5wvagQZwI7jqDpT7MTNq9QM0Qn0hF03zE7pvwoE=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ot0jmr5b7a-mlv21k5csn.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-17T17:31:37.3669894+00:00"}, "yU5W+nk1WE7Tu5E6HxqYp7notJ+hKu2dnjTZIHAZEcU=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\it5y7ywvd8-9w0m3sjok2.gz", "SourceId": "PetCareWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "PetCareWebsite#[.{fingerprint=9w0m3sjok2}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PetCareWebsite.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qjsdkvwgiz", "Integrity": "xkEYjDNAMIVxAOiUMKajrSi2PqJBlHtBlkZ/DW9IcKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PetCareWebsite.styles.css", "FileLength": 544, "LastWriteTime": "2025-07-17T17:31:37.3669894+00:00"}, "LFeO0ZvRNumQWnOkWLjqu9KI8+EWyQdplKWEpC8aaCk=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\lzbzkxbats-9w0m3sjok2.gz", "SourceId": "PetCareWebsite", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "PetCareWebsite#[.{fingerprint=9w0m3sjok2}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PetCareWebsite.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qjsdkvwgiz", "Integrity": "xkEYjDNAMIVxAOiUMKajrSi2PqJBlHtBlkZ/DW9IcKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PetCareWebsite.bundle.scp.css", "FileLength": 544, "LastWriteTime": "2025-07-17T17:31:37.3669894+00:00"}, "bMkikATF93T8gIrJoxnGtKtpzK1YyGVy0fAssOjvcZ4=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ojy8oh3fzj-aulzgniodn.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/petcare-animations#[.{fingerprint=aulzgniodn}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\petcare-animations.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pettk55eae", "Integrity": "3y2w3bGxg3T5TKAS2evlG6TVlu/TlGvFHfgp0Hc2yB8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\petcare-animations.css", "FileLength": 1948, "LastWriteTime": "2025-07-25T00:42:58.8123679+00:00"}, "0ful1CDtYqXOz8znnJw5ifmkkLOwkHn0/ColK0/syqQ=": {"Identity": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\ntu4j8ewzl-debp5faei5.gz", "SourceId": "PetCareWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\PetCareWebsite\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PetCareWebsite", "RelativePath": "css/site#[.{fingerprint=debp5faei5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j3qcc5nwxa", "Integrity": "4cDiaaGTA4+p3/fvp2O/l6xRYvU5MGGA0BzKeiEnmzA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\PetCareWebsite\\wwwroot\\css\\site.css", "FileLength": 3785, "LastWriteTime": "2025-07-25T00:42:58.8133665+00:00"}}, "CachedCopyCandidates": {}}